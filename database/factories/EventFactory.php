<?php

namespace Database\Factories;

use <PERSON>\ProjectManagementAssistant\Models\Category;
use <PERSON>\ProjectManagementAssistant\Models\Event;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Event>
 */
class EventFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('-1 week', '+1 month');

        return [
            'category_id' => Category::factory(),
            'name' => $this->faker->sentence(3),
            'description' => $this->faker->text(200),
            'start_date' => $startDate,
            'end_date' => $this->faker->dateTimeBetween($startDate, '+2 months'),
            'bg_color' => $this->faker->hexColor(),
            'fg_color' => $this->faker->hexColor(),
            'image' => $this->faker->imageUrl(800, 600, 'events'),
        ];
    }
}

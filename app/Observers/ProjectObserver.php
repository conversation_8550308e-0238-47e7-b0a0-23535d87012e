<?php

namespace Alison\ProjectManagementAssistant\Observers;

use <PERSON>\ProjectManagementAssistant\Models\Project;
use Illuminate\Support\Facades\Cache;

class ProjectObserver
{
    /**
     * Handle the Project "created" event.
     */
    public function created(Project $project): void
    {
        $this->clearCache($project);
    }

    /**
     * Handle the Project "updated" event.
     */
    public function updated(Project $project): void
    {
        $this->clearCache($project);
    }

    /**
     * Handle the Project "deleted" event.
     */
    public function deleted(Project $project): void
    {
        $this->clearCache($project, true);
    }

    /**
     * Clear all related cache entries for a project
     */
    private function clearCache(Project $project, bool $deleted = false): void
    {
        // Clear project show page cache for all users
        Cache::tags(['project_' . $project->id])->flush();

        // Clear projects index cache for all roles
        $roles = ['admin', 'teacher', 'student'];
        foreach ($roles as $role) {
            Cache::forget('projects_index_*_'.$role.'_*');
        }

        // If project has an assigned user, clear their dashboard cache
        if ($project->assigned_to) {
            Cache::forget('dashboard_' . $project->assigned_to . '_student');
            Cache::forget('projects_info_' . $project->assigned_to . '_student');
        }

        // Clear supervisor's dashboard cache if exists
        if ($project->supervisor && $project->supervisor->user) {
            Cache::forget('dashboard_' . $project->supervisor->user_id . '_teacher');
            Cache::forget('projects_info_' . $project->supervisor->user_id . '_teacher');
        }

        // Clear admin dashboard cache
        $adminCacheKey = 'dashboard_*_admin';
        Cache::forget($adminCacheKey);
    }
}

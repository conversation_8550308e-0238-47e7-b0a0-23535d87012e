<?php

namespace Tests\Feature;

use <PERSON>\ProjectManagementAssistant\Models\User;
use <PERSON>\ProjectManagementAssistant\Models\Event;
use <PERSON>\ProjectManagementAssistant\Models\Category;
use <PERSON>\ProjectManagementAssistant\Models\Project;
use <PERSON>\ProjectManagementAssistant\Models\Message;
use <PERSON>\ProjectManagementAssistant\Models\Supervisor;
use <PERSON>\ProjectManagementAssistant\Models\Offer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Spatie\Permission\Models\Role;
use Carbon\Carbon;

class DashboardTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Створення ролей
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'teacher']);
        Role::create(['name' => 'student']);
    }

    /** @test */
    public function student_can_view_dashboard_with_messages_and_events()
    {
        // Створення студента
        $student = User::factory()->create([
            'course_number' => 2,
            'email' => '<EMAIL>'
        ]);
        $student->assignRole('student');

        // Створення категорії для 2 курсу
        $category = Category::factory()->create(['course_number' => 2]);
        
        // Створення активної події
        $event = Event::factory()->create([
            'category_id' => $category->id,
            'start_date' => Carbon::now()->subDays(1),
            'end_date' => Carbon::now()->addDays(10)
        ]);

        // Створення викладача та керівника
        $teacher = User::factory()->create(['email' => '<EMAIL>']);
        $teacher->assignRole('teacher');
        
        $supervisor = Supervisor::factory()->create([
            'event_id' => $event->id,
            'user_id' => $teacher->id
        ]);

        // Створення проекту та призначення студента
        $project = Project::factory()->create([
            'event_id' => $event->id,
            'supervisor_id' => $supervisor->id,
            'assigned_to' => $student->id
        ]);

        // Створення непрочитаного повідомлення
        $message = Message::factory()->create([
            'project_id' => $project->id,
            'sender_id' => $teacher->id,
            'is_read' => false
        ]);

        // Тестування доступу до dashboard
        $response = $this->actingAs($student)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('dashboard');
        $response->assertViewHas(['unreadMessages', 'activeEvents', 'projectsInfo']);
        
        // Перевірка, що дані передаються правильно
        $unreadMessages = $response->viewData('unreadMessages');
        $activeEvents = $response->viewData('activeEvents');
        $projectsInfo = $response->viewData('projectsInfo');

        $this->assertCount(1, $unreadMessages);
        $this->assertCount(1, $activeEvents);
        $this->assertEquals('student', $projectsInfo['type']);
        $this->assertCount(1, $projectsInfo['assigned_projects']);
    }

    /** @test */
    public function teacher_can_view_dashboard_with_statistics()
    {
        // Створення викладача
        $teacher = User::factory()->create(['email' => '<EMAIL>']);
        $teacher->assignRole('teacher');

        // Створення категорії
        $category = Category::factory()->create(['course_number' => 1]);
        
        // Створення активної події
        $event = Event::factory()->create([
            'category_id' => $category->id,
            'start_date' => Carbon::now()->subDays(1),
            'end_date' => Carbon::now()->addDays(10)
        ]);

        // Створення керівника
        $supervisor = Supervisor::factory()->create([
            'event_id' => $event->id,
            'user_id' => $teacher->id
        ]);

        // Створення проектів
        $project1 = Project::factory()->create([
            'event_id' => $event->id,
            'supervisor_id' => $supervisor->id,
            'assigned_to' => null
        ]);

        $student = User::factory()->create([
            'course_number' => 1,
            'email' => '<EMAIL>'
        ]);
        $student->assignRole('student');

        $project2 = Project::factory()->create([
            'event_id' => $event->id,
            'supervisor_id' => $supervisor->id,
            'assigned_to' => $student->id
        ]);

        // Створення заявки
        $offer = Offer::factory()->create([
            'project_id' => $project1->id,
            'student_id' => $student->id
        ]);

        // Тестування доступу до dashboard
        $response = $this->actingAs($teacher)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('dashboard');
        
        $projectsInfo = $response->viewData('projectsInfo');
        
        $this->assertEquals('teacher', $projectsInfo['type']);
        $this->assertCount(2, $projectsInfo['supervised_projects']);
        $this->assertCount(1, $projectsInfo['pending_offers']);
        $this->assertEquals(2, $projectsInfo['statistics']['total_projects']);
        $this->assertEquals(1, $projectsInfo['statistics']['assigned_projects']);
        $this->assertEquals(1, $projectsInfo['statistics']['unassigned_projects']);
        $this->assertEquals(1, $projectsInfo['statistics']['pending_offers']);
    }

    /** @test */
    public function admin_can_view_dashboard_with_global_statistics()
    {
        // Створення адміністратора
        $admin = User::factory()->create(['email' => '<EMAIL>']);
        $admin->assignRole('admin');

        // Створення тестових даних
        $category = Category::factory()->create(['course_number' => 1]);
        
        $event = Event::factory()->create([
            'category_id' => $category->id,
            'start_date' => Carbon::now()->subDays(1),
            'end_date' => Carbon::now()->addDays(10)
        ]);

        $teacher = User::factory()->create(['email' => '<EMAIL>']);
        $teacher->assignRole('teacher');
        
        $supervisor = Supervisor::factory()->create([
            'event_id' => $event->id,
            'user_id' => $teacher->id
        ]);

        Project::factory()->count(3)->create([
            'event_id' => $event->id,
            'supervisor_id' => $supervisor->id
        ]);

        $student = User::factory()->create([
            'course_number' => 1,
            'email' => '<EMAIL>'
        ]);
        $student->assignRole('student');

        Offer::factory()->count(2)->create([
            'student_id' => $student->id
        ]);

        // Тестування доступу до dashboard
        $response = $this->actingAs($admin)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('dashboard');
        
        $projectsInfo = $response->viewData('projectsInfo');
        
        $this->assertEquals('admin', $projectsInfo['type']);
        $this->assertEquals(3, $projectsInfo['statistics']['total_projects']);
        $this->assertEquals(1, $projectsInfo['statistics']['active_events']);
        $this->assertEquals(2, $projectsInfo['statistics']['pending_offers']);
    }

    /** @test */
    public function dashboard_shows_empty_state_when_no_data()
    {
        // Створення студента без даних
        $student = User::factory()->create([
            'course_number' => 2,
            'email' => '<EMAIL>'
        ]);
        $student->assignRole('student');

        // Тестування доступу до dashboard
        $response = $this->actingAs($student)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Немає нових повідомлень');
        $response->assertSee('Немає активних подій');
    }
}

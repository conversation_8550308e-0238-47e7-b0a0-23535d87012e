# Аналіз моделей та міграцій бази даних

## 1. User (Користувач)

### Атрибути:
- `id` (ulid, primary key)
- `name` (string, 32, unique)
- `email` (string, unique)
- `email_verified_at` (timestamp, nullable)
- `password` (string)
- `google_id` (string, nullable)
- `first_name` (string, 50)
- `last_name` (string, 50)
- `middle_name` (string, 50, nullable)
- `description` (string, 512, nullable)
- `avatar` (string, 2048, nullable)
- `course_number` (integer, nullable)
- `remember_token` (string, nullable)
- `profile_photo_path` (string, 2048, nullable)
- `two_factor_secret` (text, nullable)
- `two_factor_recovery_codes` (text, nullable)
- `two_factor_confirmed_at` (timestamp, nullable)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Зв'язки:
- `supervisors()`: <PERSON><PERSON>any - Користувач може бути керівником (Supervisor)
- `projects()`: Has<PERSON>any - Користувачу можуть бути призначені проекти
- `offers()`: HasMany - Користувач може мати пропозиції до проектів

### Нормалізація:
- 1НФ: Унікальний ідентифікатор, атомарні значення, унікальні імена
- 2НФ: Всі неключові атрибути залежать від первинного ключа
- 3НФ: Відсутні транзитивні залежності

## 2. Category (Категорія)

### Атрибути:
- `id` (ulid, primary key)
- `name` (string, 32)
- `freezing_period` (integer, nullable)
- `course_number` (integer, nullable)
- `period` (integer)
- `attachments` (jsonb, nullable)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Зв'язки:
- `subjects()`: BelongsToMany - Багато-до-багатьох з Subject через таблицю category_subject

### Нормалізація:
- 1НФ: Унікальний ідентифікатор, атомарні значення
- 2НФ: Всі неключові атрибути залежать від первинного ключа
- 3НФ: Відсутні транзитивні залежності

## 3. Subject (Предмет)

### Атрибути:
- `id` (ulid, primary key)
- `slug` (string, 72, unique)
- `name` (string, 128)
- `course_number` (integer, nullable)
- `description` (text, nullable)
- `image` (string, 2048, nullable)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Зв'язки:
- `categories()`: BelongsToMany - Багато-до-багатьох з Category через таблицю category_subject

### Нормалізація:
- 1НФ: Унікальний ідентифікатор, атомарні значення, унікальний slug
- 2НФ: Всі неключові атрибути залежать від первинного ключа
- 3НФ: Відсутні транзитивні залежності

## 4. Event (Подія)

### Атрибути:
- `id` (ulid, primary key)
- `category_id` (ulid, foreign key to categories)
- `name` (string, 128)
- `description` (string, 512, nullable)
- `start_date` (timestamp)
- `end_date` (timestamp, nullable)
- `bg_color` (char, 7, nullable)
- `fg_color` (char, 7, nullable)
- `image` (string, 2048, nullable)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Зв'язки:
- `category()`: BelongsTo - Належить до категорії
- `supervisors()`: HasMany - Має багато керівників
- `projects()`: HasMany - Має багато проектів

### Нормалізація:
- 1НФ: Унікальний ідентифікатор, атомарні значення
- 2НФ: Всі неключові атрибути залежать від первинного ключа
- 3НФ: Відсутні транзитивні залежності

## 5. Supervisor (Керівник)

### Атрибути:
- `id` (ulid, primary key)
- `event_id` (ulid, foreign key to events)
- `user_id` (ulid, foreign key to users)
- `note` (string, 255, nullable)
- `slot_count` (integer, nullable)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Зв'язки:
- `event()`: BelongsTo - Належить до події
- `user()`: BelongsTo - Належить до користувача
- `projects()`: HasMany - Має багато проектів

### Нормалізація:
- 1НФ: Унікальний ідентифікатор, атомарні значення
- 2НФ: Всі неключові атрибути залежать від первинного ключа
- 3НФ: Відсутні транзитивні залежності

## 6. Project (Проект)

### Атрибути:
- `id` (ulid, primary key)
- `event_id` (ulid, foreign key to events, nullable)
- `supervisor_id` (ulid, foreign key to supervisors, nullable)
- `assigned_to` (ulid, foreign key to users, nullable)
- `slug` (string, 128, unique)
- `name` (string, 248, unique)
- `appendix` (string, 512, nullable)
- `body` (text, nullable)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Зв'язки:
- `event()`: BelongsTo - Належить до події
- `technologies()`: BelongsToMany - Багато-до-багатьох з Technology через таблицю project_technology
- `supervisor()`: BelongsTo - Має керівника
- `assignedTo()`: BelongsTo - Призначений користувач
- `offers()`: HasMany - Має багато пропозицій
- `messages()`: HasMany - Має багато повідомлень

### Нормалізація:
- 1НФ: Унікальний ідентифікатор, атомарні значення, унікальні ім'я та slug
- 2НФ: Всі неключові атрибути залежать від первинного ключа
- 3НФ: Відсутні транзитивні залежності

## 7. Technology (Технологія)

### Атрибути:
- `id` (ulid, primary key)
- `slug` (string, 128, unique)
- `name` (string, 128)
- `description` (text, nullable)
- `image` (string, 2048, nullable)
- `link` (string, 2048, nullable)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Зв'язки:
- `projects()`: BelongsToMany - Багато-до-багатьох з Project через таблицю project_technology

### Нормалізація:
- 1НФ: Унікальний ідентифікатор, атомарні значення, унікальний slug
- 2НФ: Всі неключові атрибути залежать від первинного ключа
- 3НФ: Відсутні транзитивні залежності

## 8. Offer (Пропозиція)

### Атрибути:
- `project_id` (ulid, primary key, foreign key to projects)
- `student_id` (ulid, primary key, foreign key to users)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Зв'язки:
- `student()`: BelongsTo - Належить до студента (користувача)
- `project()`: BelongsTo - Належить до проекту

### Нормалізація:
- 1НФ: Складений первинний ключ, атомарні значення
- 2НФ: Всі неключові атрибути залежать від всього складеного ключа
- 3НФ: Відсутні транзитивні залежності

## 9. Message (Повідомлення)

### Атрибути:
- `id` (ulid, primary key)
- `project_id` (ulid, foreign key to projects)
- `sender_id` (ulid, foreign key to users)
- `message` (text)
- `is_read` (boolean, default: false)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Зв'язки:
- `project()`: BelongsTo - Належить до проекту
- `sender()`: BelongsTo - Надсилач (користувач)

### Нормалізація:
- 1НФ: Унікальний ідентифікатор, атомарні значення
- 2НФ: Всі неключові атрибути залежать від первинного ключа
- 3НФ: Відсутні транзитивні залежності

## Висновки щодо нормалізації

Усі таблиці в базі даних знаходяться як мінімум у третій нормальній формі (3НФ), оскільки:

1. Кожна таблиця має первинний ключ, який ідентифікує кожен запис унікальним чином.
2. Всі неключові атрибути залежать від всього первинного ключа (2НФ).
3. Відсутні транзитивні залежності між неключовими атрибутами (3НФ).

Додатково, для покращення цілісності даних використовуються зовнішні ключі з каскадним видаленням, що забезпечує реляційну цілісність між пов'язаними таблицями.
